/**
 * AI 云对象主模块
 *
 * 提供智能任务执行和流式聊天功能：
 * - 任务意图识别和执行计划生成
 * - 多步骤任务的自动化执行
 * - 实时流式聊天响应
 * - Todo 工具集成和测试功能
 * - 执行上下文管理和数据传递
 */

/**
 * 执行上下文管理器
 * 用于管理任务执行过程中的所有数据和状态信息
 *
 * 数据结构：
 * - stepResults: Map<stepId, {result, metadata}> 存储步骤执行结果
 * - contextData: Map<key, value> 存储提取的上下文数据
 * - metadata: Object 存储执行过程的元数据信息
 *
 * 核心功能：
 * - 步骤结果管理：存储和检索每个步骤的执行结果
 * - 上下文数据提取：根据 AI 提取的实体，从结果中提取关键信息
 * - 项目智能匹配：基于 AI 提取的项目名称，匹配最相关的项目
 */
const { IntelligentExecutionPlanner } = require('./modules/planner')
const { executeRobustPlan } = require('./modules/executor')
const { globalPerformanceMonitor } = require('./modules/performance')
const { OpenAI } = require('openai')
const { DEFAULT_SYSTEM_PROMPT, doubaoParams } = require('./modules/config.js')
const { ExecutionContextManager } = require('./modules/context')
const TodoTool = require('./modules/todo')

module.exports = {
  async chatStreamSSE({ channel, message, messages: history_records }) {
    // 参数验证阶段：确保必需参数存在，避免后续处理出错
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    if (!channel) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: 'SSE Channel 不能为空',
      }
    }

    try {
      // 反序列化 SSE Channel，将前端传递的 channel 对象转换为可用的推送通道
      const sseChannel = uniCloud.deserializeSSEChannel(channel)

      // 初始化豆包 AI 客户端，使用配置文件中的参数
      const openai = new OpenAI(doubaoParams)

      // 构建对话消息数组：系统提示词 + 历史记录 + 当前用户消息
      const messages = [
        {
          role: 'system',
          content: DEFAULT_SYSTEM_PROMPT,
        },
        ...history_records,
      ]
      if (message)
        messages.push({
          role: 'user',
          content: message,
        })

      // 推送开始消息，通知前端开始处理用户请求
      await sseChannel.write({
        type: 'start',
        message: '开始生成回复...',
        timestamp: Date.now(),
      })

      // 创建流式 AI 响应，启用实时数据传输
      const streamResponse = await openai.chat.completions.create({
        messages, // 对话上下文
        model: 'doubao-seed-1-6-250615', // 使用的 AI 模型
        stream: true, // 启用流式响应，关键设置
        timeout: 300000, // 5 分钟超时（毫秒），防止长时间等待
      })

      // 初始化流式处理相关变量
      let fullContent = '' // 累积的完整 AI 响应内容，用于最终的意图解析
      let chunkCount = 0 // 推送的数据块计数，用于统计和调试
      let intentType = null // 识别的意图类型：task|chat
      let isChatReplyStarted = false // 是否开始推送闲聊回复的标志位
      let chatReply = '' // 提取的闲聊回复内容，仅用于 chat 类型

      // 正则表达式：匹配 AI 返回的意图类型和闲聊回复
      // task 类型只有意图类型，chat 类型有意图类型和闲聊回复
      const intentTypeRegex = /「意图类型」：(task|chat)/
      const chatReplyRegex = /「闲聊回复」：([\s\S]*)/

      // 流式处理 AI 响应数据
      for await (const chunk of streamResponse) {
        // 提取当前数据块的内容，处理可能的空值情况
        const content = chunk.choices[0]?.delta?.content || ''

        if (content) {
          fullContent += content // 累积完整内容，用于后续的正则匹配
          chunkCount++ // 增加数据块计数，用于统计和调试

          // 第一阶段：检测意图类型
          // 在累积的内容中查找意图类型标识，一旦找到就立即处理
          if (!intentType) {
            const typeMatch = intentTypeRegex.exec(fullContent)
            if (typeMatch) {
              intentType = typeMatch[1] // 提取意图类型：task|chat

              // 立即推送意图类型到前端，让用户知道 AI 已经理解了请求类型
              await sseChannel.write({
                type: 'intent_type',
                intentType: intentType,
                timestamp: Date.now(),
              })
              continue // 跳过当前块的推送，避免重复发送相同信息
            }
          }

          // 第二阶段：根据意图类型处理内容推送
          if (intentType === 'chat') {
            // chat 类型：检测闲聊回复开始标识
            if (!isChatReplyStarted) {
              const replyMatch = chatReplyRegex.exec(fullContent)
              if (replyMatch) {
                isChatReplyStarted = true
                chatReply = replyMatch[1] // 提取已有的闲聊回复内容

                // 推送闲聊回复开始标识
                await sseChannel.write({
                  type: 'content_chunk',
                  content: chatReply,
                  timestamp: Date.now(),
                })
                continue // 跳过当前块的推送，避免重复发送
              }
            } else {
              // 持续推送闲聊回复内容块
              await sseChannel.write({
                type: 'content_chunk',
                content: content,
                timestamp: Date.now(),
              })
              chatReply += content // 累积闲聊回复内容
            }
          } else if (intentType === 'task') {
            // task 类型：不需要推送额外内容，直接进入任务执行阶段
          } else {
            // 尚未检测到意图类型，继续累积内容
          }
        }
      }

      // 任务执行阶段：task 类型触发智能任务执行
      // 只有当识别到任务意图时才执行任务，chat 类型直接返回对话结果
      if (intentType === 'task') {
        // 创建执行上下文管理器，用于管理执行过程中的数据和状态
        const context = new ExecutionContextManager(IntelligentExecutionPlanner.generateUUID())

        // 使用智能执行计划生成器创建执行计划
        // AI 会分析用户意图，并返回包含步骤和提取实体的计划
        const executionPlan = await IntelligentExecutionPlanner.generatePlan(message, intentType)

        // 如果生成了有效的执行计划，则开始执行任务
        if (executionPlan.totalSteps > 0) {
          // 使用增强的执行引擎执行计划
          // 集成了错误处理、重试机制、性能监控等高级功能
          await executeRobustPlan(executionPlan, context, sseChannel, globalPerformanceMonitor)

          // 返回任务执行结果，包含详细的执行信息和性能报告
          return {
            errCode: 0,
            errMsg: 'success',
            data: {
              type: 'task_executed', // 标识这是一个任务执行结果
              intentType: intentType, // 识别的意图类型
              executionPlan: executionPlan, // 完整的执行计划信息
              contextData: Array.from(context.contextData.keys()), // 上下文数据键列表
              executionTime: executionPlan.totalExecutionTime, // 总执行时间
              performanceReport: globalPerformanceMonitor.getPerformanceReport(), // 性能报告
              content: fullContent, // AI 完整回复内容
              totalChunks: chunkCount, // 推送的数据块总数
            },
          }
        }
      }

      // 推送结束消息，标识流式聊天处理完成
      // 这是正常流程的结束，通知前端可以停止等待更多数据
      await sseChannel.end({
        type: 'end',
        content: intentType === 'chat' ? chatReply : fullContent, // chat 类型返回闲聊回复，task 类型返回完整内容
        intentType: intentType, // 识别的意图类型
        totalChunks: chunkCount, // 总共推送的数据块数量
        timestamp: Date.now(),
      })

      // 返回流式聊天完成的结果
      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'stream_complete', // 标识这是一个流式聊天完成结果
          content: intentType === 'chat' ? chatReply : fullContent,
          intentType: intentType,
          totalChunks: chunkCount,
        },
      }
    } catch (error) {
      // 错误处理：捕获并处理执行过程中的所有异常
      // 尝试通过 SSE Channel 发送错误消息给前端
      // 这样前端可以知道发生了错误，而不是一直等待
      try {
        if (channel) {
          const sseChannel = uniCloud.deserializeSSEChannel(channel)
          await sseChannel.end({
            type: 'error',
            error: error.message || '调用 AI 流式接口失败',
            timestamp: Date.now(),
          })
        }
      } catch (channelError) {
        // 如果连错误消息都发送失败，忽略该错误
      }

      // 返回错误结果给调用方
      return {
        errCode: 'API_ERROR',
        errMsg: error.message || '调用 AI 流式接口失败',
      }
    }
  },

  /**
   * 测试重构后的 todo 模块功能
   * 全面测试 CRUD 操作和认证功能
   */
  async testTodoFunctionality() {
    console.log('[TODO_TEST] ========== 开始测试重构后的 Todo 模块功能 ==========')

    const todoTool = new TodoTool()
    const testResults = {
      auth: { passed: 0, failed: 0 },
      projects: { passed: 0, failed: 0 },
      tasks: { passed: 0, failed: 0 },
      batch: { passed: 0, failed: 0 },
    }

    let testProjectId = null
    let testTaskId = null

    // ========== 认证功能测试 ==========
    console.log('[TODO_TEST] ========== 开始测试认证功能 ==========')

    // 测试 initWithToken
    try {
      console.log('[TODO_TEST] 测试 initWithToken 功能...')
      const debugToken =
        '73AE2E6CC13DD9673F421A1F3E02AED0E1BFB595FD663AFA63ED00682C85E0350ECBA76C0D9169C1842C895EC3C7FD43FA4BB3D094DAFA93E6FC18AA49B4F5302701265667560665A0D14835FCC55972EB9036F52182EC2D6CFEC251B6B3AD83385AA04082B6E13207380EE6E17F65D7D02746F0B1CB9D088DFB1EDE0D3D45D112B6963F72E74B8898CEFB2AD56ED90B75338A509771CA53C093C355F178EA86151002FFD8A51141ED48EB889B07BD4E'

      const initResult = await todoTool.initWithToken(debugToken)
      if (initResult.errCode === null) {
        console.log('[TODO_TEST] ✅ initWithToken 测试成功：', initResult.errMsg)
        testResults.auth.passed++
      } else {
        console.log('[TODO_TEST] ❌ initWithToken 测试失败：', initResult)
        testResults.auth.failed++
      }
    } catch (error) {
      console.error('[TODO_TEST] ❌ initWithToken 测试异常：', error)
      testResults.auth.failed++
    }

    // 测试 getBatchData
    try {
      console.log('[TODO_TEST] 测试 getBatchData 功能...')
      const batchResult = await todoTool.getBatchData()
      if (batchResult.errCode === null) {
        console.log('[TODO_TEST] ✅ getBatchData 测试成功，获取到数据：')
        console.log(`[TODO_TEST]   - 任务数量：${batchResult.data.tasks?.length || 0}`)
        console.log(`[TODO_TEST]   - 项目数量：${batchResult.data.projects?.length || 0}`)
        console.log(`[TODO_TEST]   - 标签数量：${batchResult.data.tags?.length || 0}`)
        testResults.batch.passed++
      } else {
        console.log('[TODO_TEST] ❌ getBatchData 测试失败：', batchResult)
        testResults.batch.failed++
      }
    } catch (error) {
      console.error('[TODO_TEST] ❌ getBatchData 测试异常：', error)
      testResults.batch.failed++
    }

    // ========== 项目管理功能测试 ==========
    console.log('[TODO_TEST] ========== 开始测试项目管理功能 ==========')

    // 测试创建项目
    try {
      console.log('[TODO_TEST] 测试创建项目功能...')
      const createProjectResult = await todoTool.createProject({
        name: '测试项目_' + Date.now(),
        color: '#e74c3c',
        kind: 'TASK',
      })

      if (createProjectResult.errCode === null) {
        testProjectId = createProjectResult.data?.id
        console.log('[TODO_TEST] ✅ 创建项目测试成功：')
        console.log(`[TODO_TEST]   - 项目 ID: ${testProjectId}`)
        console.log(`[TODO_TEST]   - 项目名称：${createProjectResult.data?.name}`)
        testResults.projects.passed++
      } else {
        console.log('[TODO_TEST] ❌ 创建项目测试失败：', createProjectResult)
        testResults.projects.failed++
      }
    } catch (error) {
      console.error('[TODO_TEST] ❌ 创建项目测试异常：', error)
      testResults.projects.failed++
    }

    // 测试获取项目列表
    try {
      console.log('[TODO_TEST] 测试获取项目列表功能...')
      const getProjectsResult = await todoTool.getProjects({ includeClosed: false })

      if (getProjectsResult.errCode === null) {
        console.log('[TODO_TEST] ✅ 获取项目列表测试成功：')
        console.log(`[TODO_TEST]   - 项目总数：${getProjectsResult.data?.length || 0}`)
        if (getProjectsResult.data && getProjectsResult.data.length > 0) {
          console.log(`[TODO_TEST]   - 第一个项目：${getProjectsResult.data[0].name}`)
        }
        testResults.projects.passed++
      } else {
        console.log('[TODO_TEST] ❌ 获取项目列表测试失败：', getProjectsResult)
        testResults.projects.failed++
      }
    } catch (error) {
      console.error('[TODO_TEST] ❌ 获取项目列表测试异常：', error)
      testResults.projects.failed++
    }

    // 测试获取单个项目详情（如果有测试项目 ID）
    if (testProjectId) {
      try {
        console.log('[TODO_TEST] 测试获取项目详情功能...')
        const getProjectResult = await todoTool.getProject(testProjectId)

        if (getProjectResult.errCode === null) {
          console.log('[TODO_TEST] ✅ 获取项目详情测试成功：')
          console.log(`[TODO_TEST]   - 项目名称：${getProjectResult.data?.name}`)
          console.log(`[TODO_TEST]   - 项目颜色：${getProjectResult.data?.color}`)
          testResults.projects.passed++
        } else {
          console.log('[TODO_TEST] ❌ 获取项目详情测试失败：', getProjectResult)
          testResults.projects.failed++
        }
      } catch (error) {
        console.error('[TODO_TEST] ❌ 获取项目详情测试异常：', error)
        testResults.projects.failed++
      }
    }

    // ========== 任务管理功能测试 ==========
    console.log('[TODO_TEST] ========== 开始测试任务管理功能 ==========')

    // 测试创建任务
    try {
      console.log('[TODO_TEST] 测试创建任务功能...')
      const createTaskData = {
        title: '测试任务_' + Date.now(),
        content: '这是一个用于测试重构后模块功能的测试任务',
        priority: 3,
        kind: 'TEXT',
      }

      // 如果有测试项目，将任务关联到项目
      if (testProjectId) {
        createTaskData.projectId = testProjectId
      }

      const createTaskResult = await todoTool.createTask(createTaskData)

      if (createTaskResult.errCode === null) {
        testTaskId = createTaskResult.data?.id
        console.log('[TODO_TEST] ✅ 创建任务测试成功：')
        console.log(`[TODO_TEST]   - 任务 ID: ${testTaskId}`)
        console.log(`[TODO_TEST]   - 任务标题：${createTaskResult.data?.title}`)
        testResults.tasks.passed++
      } else {
        console.log('[TODO_TEST] ❌ 创建任务测试失败：', createTaskResult)
        testResults.tasks.failed++
      }
    } catch (error) {
      console.error('[TODO_TEST] ❌ 创建任务测试异常：', error)
      testResults.tasks.failed++
    }

    // 测试获取任务列表
    try {
      console.log('[TODO_TEST] 测试获取任务列表功能...')
      const getTasksResult = await todoTool.getTasks({ mode: 'all', completed: false })

      if (getTasksResult.errCode === null) {
        console.log('[TODO_TEST] ✅ 获取任务列表测试成功：')
        console.log(`[TODO_TEST]   - 任务总数：${getTasksResult.data?.length || 0}`)
        if (getTasksResult.data && getTasksResult.data.length > 0) {
          console.log(`[TODO_TEST]   - 第一个任务：${getTasksResult.data[0].title}`)
        }
        testResults.tasks.passed++
      } else {
        console.log('[TODO_TEST] ❌ 获取任务列表测试失败：', getTasksResult)
        testResults.tasks.failed++
      }
    } catch (error) {
      console.error('[TODO_TEST] ❌ 获取任务列表测试异常：', error)
      testResults.tasks.failed++
    }

    // 测试获取单个任务详情（如果有测试任务 ID）
    if (testTaskId) {
      try {
        console.log('[TODO_TEST] 测试获取任务详情功能...')
        const getTaskResult = await todoTool.getTask(testTaskId)

        if (getTaskResult.errCode === null) {
          console.log('[TODO_TEST] ✅ 获取任务详情测试成功：')
          console.log(`[TODO_TEST]   - 任务标题：${getTaskResult.data?.title}`)
          console.log(`[TODO_TEST]   - 任务状态：${getTaskResult.data?.status}`)
          testResults.tasks.passed++
        } else {
          console.log('[TODO_TEST] ❌ 获取任务详情测试失败：', getTaskResult)
          testResults.tasks.failed++
        }
      } catch (error) {
        console.error('[TODO_TEST] ❌ 获取任务详情测试异常：', error)
        testResults.tasks.failed++
      }
    }

    // ========== 测试结果汇总 ==========
    console.log('[TODO_TEST] ========== 测试结果汇总 ==========')

    const totalPassed =
      testResults.auth.passed + testResults.projects.passed + testResults.tasks.passed + testResults.batch.passed
    const totalFailed =
      testResults.auth.failed + testResults.projects.failed + testResults.tasks.failed + testResults.batch.failed
    const totalTests = totalPassed + totalFailed

    console.log(`[TODO_TEST] 认证功能：✅ ${testResults.auth.passed} / ❌ ${testResults.auth.failed}`)
    console.log(`[TODO_TEST] 项目管理：✅ ${testResults.projects.passed} / ❌ ${testResults.projects.failed}`)
    console.log(`[TODO_TEST] 任务管理：✅ ${testResults.tasks.passed} / ❌ ${testResults.tasks.failed}`)
    console.log(`[TODO_TEST] 基础数据：✅ ${testResults.batch.passed} / ❌ ${testResults.batch.failed}`)
    console.log(`[TODO_TEST] 总计：✅ ${totalPassed} / ❌ ${totalFailed} (总共 ${totalTests} 个测试)`)

    const successRate = totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : 0
    console.log(`[TODO_TEST] 成功率：${successRate}%`)

    if (totalFailed === 0) {
      console.log('[TODO_TEST] 🎉 所有测试都通过了！重构后的 Todo 模块功能正常！')
    } else {
      console.log(`[TODO_TEST] ⚠️  有 ${totalFailed} 个测试失败，请检查相关功能`)
    }

    console.log('[TODO_TEST] ========== Todo 模块功能测试完成 ==========')

    return {
      errCode: null,
      errMsg: '测试完成',
      data: {
        summary: {
          totalTests,
          totalPassed,
          totalFailed,
          successRate: parseFloat(successRate),
        },
        details: testResults,
        testProjectId,
        testTaskId,
      },
    }
  },
}
